#ifndef ERRNO_HPP
# define ERRNO_HPP

extern thread_local int ft_errno;

#define ERRNO_OFFSET 2000

enum PTErrorCode
{
    ER_SUCCESS = 0,
	CMA_BAD_ALLOC,
    CMA_INVALID_PTR,
    PT_ERR_QUEUE_FULL,
    PT_ERR_MUTEX_NULLPTR,
    PT_ERR_MUTEX_OWNER,
	PT_ERR_ALRDY_LOCKED,
	PT_ALREADDY_LOCKED,
    SHARED_PTR_NULL_PTR,
    SHARED_PTR_OUT_OF_BOUNDS,
    SHARED_PTR_ALLOCATION_FAILED,
    SHARED_PTR_INVALID_OPERATION,
    SHARED_PTR_ELEMENT_ALREADDY_ADDED,
    UNIQUE_PTR_NULL_PTR,
    UNIQUE_PTR_OUT_OF_BOUNDS,
    UNIQUE_PTR_ALLOCATION_FAILED,
    UNIQUE_PTR_INVALID_OPERATION,
    MAP_ALLOCATION_FAILED,
    MAP_KEY_NOT_FOUND,
	FILE_INVALID_FD,
	FT_EINVAL,
	STRING_MEM_ALLOC_FAIL,
	STRING_ERASE_OUT_OF_BOUNDS,
	VECTOR_ALLOC_FAIL,
	VECTOR_OUT_OF_BOUNDS,
	VECTOR_INVALID_PTR,
	VECTOR_CRITICAL_ERROR,
	VECTOR_INVALID_OPERATION,
	FT_EALLOC,
	FT_ETERM,
	SOCKET_CREATION_FAILED,
    SOCKET_BIND_FAILED,
	SOCKET_LISTEN_FAILED,
    SOCKET_CONNECT_FAILED,
    INVALID_IP_FORMAT,
    UNSUPPORTED_SOCKET_TYPE,
    SOCKET_ACCEPT_FAILED,
    SOCKET_SEND_FAILED,
    SOCKET_RECEIVE_FAILED,
    SOCKET_CLOSE_FAILED,
	SOCKET_INVALID_CONFIGURATION,
	SOCKET_UNSUPPORTED_TYPE,
	SOCKET_ALRDY_INITIALIZED,
	UNORD_MAP_MEMORY,
	UNORD_MAP_NOT_FOUND,
	UNORD_MAP_UNKNOWN,
	DECK_EMPTY,
	DECK_ALLOC_FAIL,
	BUTTON_ALLOC_FAIL,
	SFML_WINDOW_CREATE_FAIL,
    CHECK_DIR_FAIL,
    JSON_MALLOC_FAIL,
    MAP3D_ALLOC_FAIL,
    MAP3D_OUT_OF_BOUNDS,
    SOCKET_JOIN_GROUP_FAILED,
    CHARACTER_INVENTORY_FULL,
    CHARACTER_LEVEL_TABLE_INVALID,
	GAME_GENERAL_ERROR,
	GAME_INVALID_MOVE,
};

const char* ft_strerror(int error_code);
void		ft_perror(const char *error_msg);

#endif
