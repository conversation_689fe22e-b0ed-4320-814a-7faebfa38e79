#ifndef SFMLGRAPHICS_HPP
#define SFMLGRAPHICS_HPP

#include "../IGraphicsLibrary.hpp"
#include "../MenuSystem.hpp"
#include <SFML/Graphics.hpp>
#include <string>
#include <vector>

class SFMLGraphics : public IGraphicsLibrary {
public:
    SFMLGraphics();
    virtual ~SFMLGraphics();

    // IGraphicsLibrary interface implementation
    virtual int initialize() override;
    virtual void shutdown() override;
    virtual void render(const game_data& game) override;
    virtual GameKey getInput() override;
    virtual const char* getName() const override;
    virtual bool shouldContinue() const override;
    virtual const char* getError() const override;
    virtual void setFrameRate(int fps) override;
    virtual void setMenuSystem(MenuSystem* menuSystem) override;
    virtual void setSwitchMessage(const std::string& message, int timer) override;

private:
    bool _initialized;
    bool _shouldContinue;
    bool _isShuttingDown;
    std::string _errorMessage;
    int _targetFPS;

    // SFML objects
    sf::RenderWindow* _window;
    sf::Font _font;
    sf::Font _fontLarge;
    sf::Font _fontMedium;
    sf::Font _fontSmall;

    // Menu system
    MenuSystem* _menuSystem;

    // Switch message display
    std::string _switchMessage;
    int _switchMessageTimer;

    // Window dimensions
    static const int WINDOW_WIDTH = 800;
    static const int WINDOW_HEIGHT = 600;
    static const int CELL_SIZE = 20;

    // Colors (SFML Color values)
    static const sf::Color COLOR_BACKGROUND;
    static const sf::Color COLOR_BORDER;
    static const sf::Color COLOR_SNAKE_HEAD;
    static const sf::Color COLOR_SNAKE_BODY;
    static const sf::Color COLOR_FOOD;
    static const sf::Color COLOR_TEXT;
    static const sf::Color COLOR_SELECTOR_BG;
    static const sf::Color COLOR_SELECTED_TEXT;

    // Helper methods
    void setError(const std::string& error);
    void clearError();
    void drawRect(int x, int y, int width, int height, const sf::Color& color, bool filled = true);
    void drawTransparentRect(int x, int y, int width, int height, const sf::Color& color, unsigned char alpha);
    GameKey translateSFMLKey(sf::Keyboard::Key key);
    void calculateGameArea(const game_data& game, int& offsetX, int& offsetY, int& cellSize);

    // Font methods
    bool initializeFonts();
    void shutdownFonts();
    void drawTextWithFont(const std::string& text, int x, int y, const sf::Font& font, int fontSize, const sf::Color& color = COLOR_TEXT);
    void drawCenteredTextWithFont(const std::string& text, int y, const sf::Font& font, int fontSize, const sf::Color& color = COLOR_TEXT);
    sf::Vector2f getTextSize(const std::string& text, const sf::Font& font, int fontSize);

    // Menu rendering methods
    void renderMenu();
    void renderMainMenu();
    void renderSettingsMenu();
    void renderCreditsMenu();
    void renderInstructionsMenu();
    void renderGameOverMenu();
    void drawCenteredText(const std::string& text, int y, const sf::Color& color = COLOR_TEXT);
    void drawMenuItems(const std::vector<MenuItem>& items, int selectedIndex, int startY);
};

#endif // SFMLGRAPHICS_HPP
