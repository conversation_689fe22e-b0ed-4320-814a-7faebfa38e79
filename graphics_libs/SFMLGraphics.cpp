#include "SFMLGraphics.hpp"
#include "../game_data.hpp"
#include <iostream>
#include <sstream>
#include <algorithm>
#include <thread>
#include <chrono>

// Color definitions
const sf::Color SFMLGraphics::COLOR_BACKGROUND(20, 20, 30);        // Dark blue-gray
const sf::Color SFMLGraphics::COLOR_BORDER(100, 100, 120);         // Light gray
const sf::Color SFMLGraphics::COLOR_SNAKE_HEAD(50, 200, 50);       // Bright green
const sf::Color SFMLGraphics::COLOR_SNAKE_BODY(30, 150, 30);       // Dark green
const sf::Color SFMLGraphics::COLOR_FOOD(200, 50, 50);             // Red
const sf::Color SFMLGraphics::COLOR_TEXT(255, 255, 255);           // White

// Additional colors for better UI
const sf::Color SFMLGraphics::COLOR_SELECTOR_BG(70, 130, 180, 120); // Steel blue with transparency
const sf::Color SFMLGraphics::COLOR_SELECTED_TEXT(255, 255, 255);   // White text for selected items

SFMLGraphics::SFMLGraphics()
    : _initialized(false), _shouldContinue(true), _isShuttingDown(false), _targetFPS(60),
      _window(nullptr), _menuSystem(nullptr), _switchMessageTimer(0) {
}

SFMLGraphics::~SFMLGraphics() {
    std::cout << "DEBUG: SFML destructor called on instance: " << this << std::endl;
    std::cout << "DEBUG: SFML destructor - _initialized: " << _initialized << std::endl;
    shutdown();
    std::cout << "DEBUG: SFML destructor complete" << std::endl;
}

int SFMLGraphics::initialize() {
    if (_initialized) {
        return 0; // Already initialized
    }

    clearError();

    try {
        // Create SFML window (SFML 3.0 API)
        _window = new sf::RenderWindow(sf::VideoMode({WINDOW_WIDTH, WINDOW_HEIGHT}),
                                       "Nibbler - SFML Graphics (Press 1/2/3 to switch libraries)",
                                       sf::Style::Titlebar | sf::Style::Close);

        if (!_window) {
            setError("Failed to create SFML window");
            return -1;
        }

        // Make window more visible and request focus
        _window->setPosition({100, 100}); // Position it away from terminal
        _window->requestFocus(); // Request focus so it can receive key events

        // Check if window was created successfully
        if (!_window->isOpen()) {
            setError("SFML window failed to open");
            delete _window;
            _window = nullptr;
            return -1;
        }

        // Set frame rate
        _window->setFramerateLimit(_targetFPS);

        // Request focus for the window
        _window->requestFocus();

        // Initialize fonts
        if (!initializeFonts()) {
            setError("Failed to initialize fonts");
            return -1;
        }

        _initialized = true;
        _shouldContinue = true;
        _isShuttingDown = false;

        // Clear the window and display it once to make it visible
        _window->clear(COLOR_BACKGROUND);
        _window->display();

        // Additional focus request after display
        _window->requestFocus();

        return 0;
    }
    catch (const std::exception& e) {
        setError(std::string("SFML initialization failed: ") + e.what());
        return -1;
    }
}

void SFMLGraphics::shutdown() {
    std::cout << "DEBUG: SFML shutdown() called from:" << std::endl;
    std::cout << "DEBUG: SFML instance pointer: " << this << std::endl;
    std::cout << "DEBUG: SFML _initialized flag: " << _initialized << std::endl;
    if (!_initialized) {
        std::cout << "DEBUG: SFML shutdown() - already not initialized" << std::endl;
        return;
    }

    std::cout << "DEBUG: SFML shutdown() - setting shutdown flag" << std::endl;
    // Set shutdown flag to prevent getInput from setting _shouldContinue to false
    _isShuttingDown = true;

    std::cout << "DEBUG: SFML shutdown() - processing remaining events" << std::endl;
    // Process any remaining events to ensure clean shutdown
    if (_window && _window->isOpen()) {
        std::cout << "DEBUG: SFML shutdown() - window is open, polling events" << std::endl;

        // Make window invisible immediately to smooth transition (similar to SDL2's hide)
        _window->setVisible(false);

        // Poll all remaining events to clear the event queue
        while (auto event = _window->pollEvent()) {
            // Just consume the events, don't process them
        }

        std::cout << "DEBUG: SFML shutdown() - closing window" << std::endl;
        // Close the window
        _window->close();

        std::cout << "DEBUG: SFML shutdown() - waiting for window close" << std::endl;
        // Small delay to ensure window close is processed
        std::this_thread::sleep_for(std::chrono::milliseconds(100));

        std::cout << "DEBUG: SFML shutdown() - polling final events" << std::endl;
        // Poll events one more time after closing - but be careful not to access closed window
        if (_window->isOpen()) {
            while (auto event = _window->pollEvent()) {
                // Consume any remaining events
            }
        }
    }

    std::cout << "DEBUG: SFML shutdown() - shutting down fonts" << std::endl;
    // Shutdown fonts
    shutdownFonts();

    std::cout << "DEBUG: SFML shutdown() - deleting window object" << std::endl;
    // Delete the window object
    if (_window) {
        delete _window;
        _window = nullptr;
    }

    std::cout << "DEBUG: SFML shutdown() - clearing messages" << std::endl;
    // Clear any switch messages
    _switchMessage.clear();
    _switchMessageTimer = 0;

    std::cout << "DEBUG: SFML shutdown() - resetting flags" << std::endl;
    // Reset shutdown flag
    _isShuttingDown = false;

    _initialized = false;
    std::cout << "DEBUG: SFML shutdown() - complete" << std::endl;
}

bool SFMLGraphics::initializeFonts() {
    // Try to load fonts from common system locations
    const char* fontPaths[] = {
        // macOS - Arial fonts
        "/System/Library/Fonts/Supplemental/Arial.ttf",
        "/System/Library/Fonts/Supplemental/Arial Unicode.ttf",
        "/System/Library/Fonts/Arial.ttf",
        "/Library/Fonts/Arial.ttf",
        // macOS - Helvetica as fallback
        "/System/Library/Fonts/HelveticaNeue.ttc",
        "/System/Library/Fonts/Geneva.ttf",
        // Windows
        "C:/Windows/Fonts/arial.ttf",
        // Linux
        "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
        "/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf",
        "/usr/share/fonts/TTF/arial.ttf",
        // Fallback - try to find any reasonable font
        "/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf",
        nullptr
    };

    bool fontLoaded = false;
    for (int i = 0; fontPaths[i] != nullptr; ++i) {
        if (_font.openFromFile(fontPaths[i])) {
            std::cout << "Loaded font: " << fontPaths[i] << std::endl;
            fontLoaded = true;
            break;
        }
    }

    if (!fontLoaded) {
        std::cerr << "Warning: Could not load any system font. Text may not display correctly." << std::endl;
        // Try to use default font (this might not work on all systems)
        return false;
    }

    // Use the same font for all sizes (we'll control size in rendering)
    _fontLarge = _font;
    _fontMedium = _font;
    _fontSmall = _font;

    return true;
}

void SFMLGraphics::shutdownFonts() {
    // SFML fonts are automatically cleaned up
}

void SFMLGraphics::render(const game_data& game) {
    if (!_initialized || !_window) {
        return;
    }

    // Clear window
    _window->clear(COLOR_BACKGROUND);

    // Handle switch message timer
    if (_switchMessageTimer > 0) {
        _switchMessageTimer--;
    }

    // Render based on menu state
    if (_menuSystem && _menuSystem->getCurrentState() != MenuState::IN_GAME) {
        renderMenu();
    } else {
        // Render game
        int offsetX, offsetY, cellSize;
        calculateGameArea(game, offsetX, offsetY, cellSize);

        // Draw game board border
        drawRect(offsetX - 2, offsetY - 2,
                static_cast<int>(game.get_width()) * cellSize + 4,
                static_cast<int>(game.get_height()) * cellSize + 4,
                COLOR_BORDER, false);

        // Draw food and snakes by scanning the map
        for (int y = 0; y < static_cast<int>(game.get_height()); ++y) {
            for (int x = 0; x < static_cast<int>(game.get_width()); ++x) {
                int mapValue = game.get_map_value(x, y, 2); // Layer 2 contains snakes and food

                if (mapValue == FOOD) {
                    // Draw food
                    drawRect(offsetX + x * cellSize, offsetY + y * cellSize,
                            cellSize, cellSize, COLOR_FOOD);
                } else if (mapValue >= SNAKE_HEAD_PLAYER_1 && mapValue < SNAKE_HEAD_PLAYER_1 + 1000) {
                    // Player 1 snake
                    sf::Color segmentColor = (mapValue == SNAKE_HEAD_PLAYER_1) ? COLOR_SNAKE_HEAD : COLOR_SNAKE_BODY;
                    drawRect(offsetX + x * cellSize, offsetY + y * cellSize,
                            cellSize, cellSize, segmentColor);
                } else if (mapValue >= SNAKE_HEAD_PLAYER_2 && mapValue < SNAKE_HEAD_PLAYER_2 + 1000) {
                    // Player 2 snake (different color)
                    sf::Color segmentColor = (mapValue == SNAKE_HEAD_PLAYER_2) ?
                        sf::Color(200, 50, 200) : sf::Color(150, 30, 150); // Purple
                    drawRect(offsetX + x * cellSize, offsetY + y * cellSize,
                            cellSize, cellSize, segmentColor);
                }
            }
        }

        // Draw game info
        std::ostringstream info;
        info << "Score: " << game.get_snake_length(0);
        if (game.get_snake_length(1) > 0) {
            info << " | Player 2: " << game.get_snake_length(1);
        }
        info << " | FPS: " << _targetFPS;
        
        drawTextWithFont(info.str(), 10, 10, _fontSmall, 16, COLOR_TEXT);

        // Draw controls
        drawTextWithFont("Controls: Arrow keys=Move, 1/2/3=Switch graphics, ESC=Menu", 
                        10, WINDOW_HEIGHT - 30, _fontSmall, 14, COLOR_TEXT);
    }

    // Draw switch message if active
    if (_switchMessageTimer > 0 && !_switchMessage.empty()) {
        drawCenteredTextWithFont(_switchMessage, WINDOW_HEIGHT / 2 + 50, _fontMedium, 20, 
                                sf::Color(255, 255, 0, 200)); // Yellow with transparency
    }

    // Display everything
    _window->display();
}

GameKey SFMLGraphics::getInput() {
    if (!_initialized || !_window) {
        return GameKey::NONE;
    }

    // During shutdown, don't process any input
    if (_isShuttingDown) {
        return GameKey::NONE;
    }

    // Check if window is still open
    if (!_window->isOpen()) {
        _shouldContinue = false;
        return GameKey::QUIT;
    }

    // Check for direct keyboard state for library switching keys (works even without window focus)
    if (sf::Keyboard::isKeyPressed(sf::Keyboard::Key::Num1)) {
        // Add a small delay to prevent multiple rapid detections
        static auto lastKey1Press = std::chrono::steady_clock::now();
        auto now = std::chrono::steady_clock::now();
        if (std::chrono::duration_cast<std::chrono::milliseconds>(now - lastKey1Press).count() > 500) {
            lastKey1Press = now;
            std::cout << "DEBUG: SFML detected KEY_1 via direct keyboard state" << std::endl;
            return GameKey::KEY_1;
        }
    }

    if (sf::Keyboard::isKeyPressed(sf::Keyboard::Key::Num2)) {
        static auto lastKey2Press = std::chrono::steady_clock::now();
        auto now = std::chrono::steady_clock::now();
        if (std::chrono::duration_cast<std::chrono::milliseconds>(now - lastKey2Press).count() > 500) {
            lastKey2Press = now;
            std::cout << "DEBUG: SFML detected KEY_2 via direct keyboard state" << std::endl;
            return GameKey::KEY_2;
        }
    }

    if (sf::Keyboard::isKeyPressed(sf::Keyboard::Key::Num3)) {
        static auto lastKey3Press = std::chrono::steady_clock::now();
        auto now = std::chrono::steady_clock::now();
        if (std::chrono::duration_cast<std::chrono::milliseconds>(now - lastKey3Press).count() > 500) {
            lastKey3Press = now;
            std::cout << "DEBUG: SFML detected KEY_3 via direct keyboard state" << std::endl;
            return GameKey::KEY_3;
        }
    }

    // SFML 3.0 API - pollEvent returns optional
    // Only poll events if window is still open and we're not shutting down
    if (_window->isOpen() && !_isShuttingDown) {
        while (auto event = _window->pollEvent()) {
            if (event->is<sf::Event::Closed>()) {
                _shouldContinue = false;
                return GameKey::QUIT;
            }

        if (const auto* keyPressed = event->getIf<sf::Event::KeyPressed>()) {
            sf::Keyboard::Key key = keyPressed->code;
            std::cout << "DEBUG: SFML key pressed: " << static_cast<int>(key) << std::endl;

            // Handle graphics switching first (works in any state) - backup method
            if (key == sf::Keyboard::Key::Num1) {
                std::cout << "DEBUG: SFML detected KEY_1 via event (backup)" << std::endl;
                return GameKey::KEY_1;
            }
            if (key == sf::Keyboard::Key::Num2) {
                std::cout << "DEBUG: SFML detected KEY_2 via event (backup)" << std::endl;
                return GameKey::KEY_2;
            }
            if (key == sf::Keyboard::Key::Num3) {
                std::cout << "DEBUG: SFML detected KEY_3 via event (backup)" << std::endl;
                return GameKey::KEY_3;
            }

            // Handle menu navigation if in menu mode
            if (_menuSystem && _menuSystem->getCurrentState() != MenuState::IN_GAME) {
                switch (key) {
                    case sf::Keyboard::Key::Up:
                        _menuSystem->navigateUp();
                        return GameKey::NONE;
                    case sf::Keyboard::Key::Down:
                        _menuSystem->navigateDown();
                        return GameKey::NONE;
                    case sf::Keyboard::Key::Enter:
                    case sf::Keyboard::Key::Space:
                        _menuSystem->selectCurrentItem();
                        return GameKey::NONE;
                    case sf::Keyboard::Key::Escape:
                        _menuSystem->goBack();
                        return GameKey::NONE;
                    default:
                        return GameKey::NONE;
                }
            }

            // In game mode - return the key for game logic to handle
            return translateSFMLKey(key);
        }
        }
    }

    return GameKey::NONE;
}

GameKey SFMLGraphics::translateSFMLKey(sf::Keyboard::Key key) {
    switch (key) {
        case sf::Keyboard::Key::Up:    return GameKey::UP;
        case sf::Keyboard::Key::Down:  return GameKey::DOWN;
        case sf::Keyboard::Key::Left:  return GameKey::LEFT;
        case sf::Keyboard::Key::Right: return GameKey::RIGHT;
        case sf::Keyboard::Key::Num1:  return GameKey::KEY_1;
        case sf::Keyboard::Key::Num2:  return GameKey::KEY_2;
        case sf::Keyboard::Key::Num3:  return GameKey::KEY_3;
        case sf::Keyboard::Key::Escape: return GameKey::ESCAPE;
        case sf::Keyboard::Key::Q:     return GameKey::ESCAPE;
        default: return GameKey::NONE;
    }
}

const char* SFMLGraphics::getName() const {
    return "SFML";
}

bool SFMLGraphics::shouldContinue() const {
    // During shutdown, we should still return true to allow library switching
    // Only return false if the user explicitly wants to quit (e.g., closed window)
    return _shouldContinue && _initialized;
}

const char* SFMLGraphics::getError() const {
    return _errorMessage.empty() ? nullptr : _errorMessage.c_str();
}

void SFMLGraphics::setFrameRate(int fps) {
    _targetFPS = fps;
    if (_window) {
        _window->setFramerateLimit(fps);
    }
}

void SFMLGraphics::setMenuSystem(MenuSystem* menuSystem) {
    _menuSystem = menuSystem;
}

void SFMLGraphics::setSwitchMessage(const std::string& message, int timer) {
    _switchMessage = message;
    _switchMessageTimer = timer;
}

void SFMLGraphics::setError(const std::string& error) {
    _errorMessage = error;
    std::cerr << "SFML Graphics Error: " << error << std::endl;
}

void SFMLGraphics::clearError() {
    _errorMessage.clear();
}

void SFMLGraphics::drawRect(int x, int y, int width, int height, const sf::Color& color, bool filled) {
    if (!_window) return;

    sf::RectangleShape rect(sf::Vector2f(width, height));
    rect.setPosition(sf::Vector2f(x, y));

    if (filled) {
        rect.setFillColor(color);
        rect.setOutlineThickness(0);
    } else {
        rect.setFillColor(sf::Color::Transparent);
        rect.setOutlineColor(color);
        rect.setOutlineThickness(1);
    }

    _window->draw(rect);
}

void SFMLGraphics::drawTransparentRect(int x, int y, int width, int height, const sf::Color& color, unsigned char alpha) {
    if (!_window) return;

    sf::RectangleShape rect(sf::Vector2f(width, height));
    rect.setPosition(sf::Vector2f(x, y));

    sf::Color transparentColor = color;
    transparentColor.a = alpha;
    rect.setFillColor(transparentColor);

    _window->draw(rect);
}

void SFMLGraphics::calculateGameArea(const game_data& game, int& offsetX, int& offsetY, int& cellSize) {
    int boardWidth = static_cast<int>(game.get_width());
    int boardHeight = static_cast<int>(game.get_height());

    // Calculate the maximum cell size that fits in the window
    int maxCellWidth = (WINDOW_WIDTH - 100) / boardWidth;
    int maxCellHeight = (WINDOW_HEIGHT - 150) / boardHeight;
    cellSize = std::min(maxCellWidth, maxCellHeight);
    cellSize = std::max(cellSize, 10); // Minimum cell size

    // Center the game area
    int gameAreaWidth = boardWidth * cellSize;
    int gameAreaHeight = boardHeight * cellSize;
    offsetX = (WINDOW_WIDTH - gameAreaWidth) / 2;
    offsetY = (WINDOW_HEIGHT - gameAreaHeight) / 2 + 20; // Offset for UI
}

void SFMLGraphics::drawTextWithFont(const std::string& text, int x, int y, const sf::Font& font, int fontSize, const sf::Color& color) {
    if (!_window || text.empty()) return;

    // Clean the text string to handle problematic characters
    std::string cleanText = text;

    // Replace or filter out problematic characters
    for (size_t i = 0; i < cleanText.length(); ++i) {
        unsigned char c = static_cast<unsigned char>(cleanText[i]);

        // Replace non-printable ASCII characters (except common whitespace)
        if (c < 32 && c != '\t' && c != '\n' && c != '\r') {
            cleanText[i] = ' '; // Replace with space
        }
        // Handle extended ASCII characters that might cause issues
        else if (c > 126 && c < 160) {
            cleanText[i] = '?'; // Replace with question mark
        }
    }

    // SFML 3.0 requires font in constructor
    sf::Text sfText(font, cleanText, fontSize);
    sfText.setFillColor(color);
    sfText.setPosition(sf::Vector2f(x, y));

    _window->draw(sfText);
}

void SFMLGraphics::drawCenteredTextWithFont(const std::string& text, int y, const sf::Font& font, int fontSize, const sf::Color& color) {
    if (!_window || text.empty()) return;

    sf::Vector2f textSize = getTextSize(text, font, fontSize);
    int x = (WINDOW_WIDTH - static_cast<int>(textSize.x)) / 2;
    drawTextWithFont(text, x, y, font, fontSize, color);
}

sf::Vector2f SFMLGraphics::getTextSize(const std::string& text, const sf::Font& font, int fontSize) {
    // SFML 3.0 requires font in constructor
    sf::Text sfText(font, text, fontSize);

    sf::FloatRect bounds = sfText.getLocalBounds();
    return sf::Vector2f(bounds.size.x, bounds.size.y);
}

void SFMLGraphics::drawCenteredText(const std::string& text, int y, const sf::Color& color) {
    drawCenteredTextWithFont(text, y, _fontMedium, 20, color);
}

void SFMLGraphics::renderMenu() {
    if (!_menuSystem) return;

    switch (_menuSystem->getCurrentState()) {
        case MenuState::MAIN_MENU:
            renderMainMenu();
            break;
        case MenuState::SETTINGS_MENU:
            renderSettingsMenu();
            break;
        case MenuState::CREDITS_PAGE:
            renderCreditsMenu();
            break;
        case MenuState::INSTRUCTIONS_PAGE:
            renderInstructionsMenu();
            break;
        case MenuState::GAME_OVER:
            renderGameOverMenu();
            break;
        default:
            break;
    }
}

void SFMLGraphics::renderMainMenu() {
    // Draw title
    drawCenteredTextWithFont("NIBBLER - SNAKE GAME", 100, _fontLarge, 32, COLOR_TEXT);

    // Draw menu items
    std::vector<MenuItem> items = _menuSystem->getCurrentMenuItems();
    drawMenuItems(items, _menuSystem->getCurrentSelection(), 200);

    // Draw instructions
    drawCenteredTextWithFont("Use Arrow Keys to navigate, ENTER to select", WINDOW_HEIGHT - 80, _fontSmall, 16, COLOR_TEXT);
    drawCenteredTextWithFont("Press 1/2/3 to switch graphics libraries", WINDOW_HEIGHT - 60, _fontSmall, 16, COLOR_TEXT);
}

void SFMLGraphics::renderSettingsMenu() {
    // Draw title
    drawCenteredTextWithFont("GAME SETTINGS", 100, _fontLarge, 32, COLOR_TEXT);

    // Draw menu items
    std::vector<MenuItem> items = _menuSystem->getCurrentMenuItems();
    drawMenuItems(items, _menuSystem->getCurrentSelection(), 180);

    // Draw instructions
    drawCenteredTextWithFont("Use Arrow Keys to navigate, ENTER to toggle/adjust", WINDOW_HEIGHT - 80, _fontSmall, 16, COLOR_TEXT);
    drawCenteredTextWithFont("ESC to go back", WINDOW_HEIGHT - 60, _fontSmall, 16, COLOR_TEXT);
}

void SFMLGraphics::renderCreditsMenu() {
    // Draw title
    drawCenteredTextWithFont("CREDITS & INFORMATION", 80, _fontLarge, 32, COLOR_TEXT);

    // Draw content
    std::vector<std::string> content = _menuSystem->getCreditsContent();
    int y = 140;
    for (const std::string& line : content) {
        if (line.empty()) {
            y += 15; // Extra space for empty lines
        } else if (line.find(":") != std::string::npos && line.find("-") == std::string::npos) {
            // Section headers
            drawCenteredTextWithFont(line, y, _fontMedium, 22, sf::Color(100, 200, 255)); // Light blue
            y += 35;
        } else {
            // Regular content
            drawCenteredTextWithFont(line, y, _fontSmall, 18, COLOR_TEXT);
            y += 25;
        }
    }

    // Draw instructions
    drawCenteredTextWithFont("ESC to go back", WINDOW_HEIGHT - 40, _fontSmall, 16, COLOR_TEXT);
}

void SFMLGraphics::renderInstructionsMenu() {
    // Draw title
    drawCenteredTextWithFont("HOW TO PLAY", 80, _fontLarge, 32, COLOR_TEXT);

    // Draw content
    std::vector<std::string> content = _menuSystem->getInstructionsContent();
    int y = 140;
    for (const std::string& line : content) {
        if (line.empty()) {
            y += 15; // Extra space for empty lines
        } else if (line.find(":") != std::string::npos && line.find("-") == std::string::npos) {
            // Section headers
            drawCenteredTextWithFont(line, y, _fontMedium, 22, sf::Color(255, 200, 100)); // Light orange
            y += 35;
        } else {
            // Regular content
            drawCenteredTextWithFont(line, y, _fontSmall, 18, COLOR_TEXT);
            y += 25;
        }
    }

    // Draw instructions
    drawCenteredTextWithFont("ESC to go back", WINDOW_HEIGHT - 40, _fontSmall, 16, COLOR_TEXT);
}

void SFMLGraphics::renderGameOverMenu() {
    // Draw title
    drawCenteredTextWithFont("GAME OVER", 120, _fontLarge, 36, sf::Color(255, 100, 100)); // Light red

    // Draw final score
    std::ostringstream scoreText;
    int finalScore = _menuSystem->getGameOverScore();
    scoreText << "Final Score: " << finalScore;

    drawCenteredTextWithFont(scoreText.str(), 180, _fontMedium, 20, COLOR_TEXT);

    // Draw menu items
    std::vector<MenuItem> items = _menuSystem->getCurrentMenuItems();
    drawMenuItems(items, _menuSystem->getCurrentSelection(), 280);

    // Draw instructions
    drawCenteredTextWithFont("Use Arrow Keys to navigate, ENTER to select", WINDOW_HEIGHT - 80, _fontSmall, 16, COLOR_TEXT);
    drawCenteredTextWithFont("ESC to quit game", WINDOW_HEIGHT - 60, _fontSmall, 16, COLOR_TEXT);
}

void SFMLGraphics::drawMenuItems(const std::vector<MenuItem>& items, int selectedIndex, int startY) {
    for (size_t i = 0; i < items.size(); ++i) {
        bool isSelected = (static_cast<int>(i) == selectedIndex);

        // Draw selection highlight with transparency
        if (isSelected) {
            // Draw a semi-transparent background for the selected item
            drawTransparentRect(50, startY + static_cast<int>(i) * 40 - 5, WINDOW_WIDTH - 100, 30, COLOR_SELECTOR_BG, 120);

            // Draw a subtle border around the selected item
            drawRect(50, startY + static_cast<int>(i) * 40 - 5, WINDOW_WIDTH - 100, 30, sf::Color(70, 130, 180), false);
        }

        // Use white text for both selected and unselected items for better readability
        drawCenteredTextWithFont(items[i].text, startY + static_cast<int>(i) * 40, _fontMedium, 20, COLOR_SELECTED_TEXT);
    }
}

// External C interface for dynamic loading
extern "C" {
    IGraphicsLibrary* createGraphicsLibrary() {
        return new SFMLGraphics();
    }

    void destroyGraphicsLibrary(IGraphicsLibrary* lib) {
        delete lib;
    }

    const char* getLibraryName() {
        return "SFML Graphics Library";
    }

    const char* getLibraryVersion() {
        return "1.0.0";
    }
}
