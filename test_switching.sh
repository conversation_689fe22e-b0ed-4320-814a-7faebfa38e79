#!/bin/bash

echo "Testing SFML ↔ NCurses switching..."
echo "Starting with SFML..."

# Start with SFML
echo "3" | timeout 30 ./nibbler 15 10 &
PID=$!

echo "SFML should now be running with PID: $PID"
echo "The SFML window should be visible."
echo ""
echo "To test the fix:"
echo "1. Make sure the SFML window is visible and click on it to give it focus"
echo "2. Press '1' on your keyboard to switch to NCurses"
echo "3. You should see debug output indicating the switch"
echo "4. Press Ctrl+C to stop the test"
echo ""
echo "Waiting for user interaction..."

# Wait for the process to finish or be killed
wait $PID
echo "Test completed."
