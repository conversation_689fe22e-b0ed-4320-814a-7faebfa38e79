#include "GameEngine.hpp"
#include "LibraryManager.hpp"
#include <iostream>
#include <thread>
#include <chrono>

int main() {
    std::cout << "=== Debug Library Switching Test ===" << std::endl;
    
    // Create library manager and load libraries
    LibraryManager libManager;
    
    std::cout << "Loading NCurses library..." << std::endl;
    if (libManager.loadLibrary("./lib_ncurses.so") != 0) {
        std::cerr << "Failed to load NCurses: " << libManager.getError() << std::endl;
        return 1;
    }
    
    std::cout << "Loading SFML library..." << std::endl;
    if (libManager.loadLibrary("./lib_sfml.so") != 0) {
        std::cerr << "Failed to load SFML: " << libManager.getError() << std::endl;
        return 1;
    }
    
    std::cout << "Libraries loaded successfully!" << std::endl;
    std::cout << "Library count: " << libManager.getLibraryCount() << std::endl;
    
    // Test switching to SFML (index 1)
    std::cout << "\n=== Testing switch to SFML (index 1) ===" << std::endl;
    if (libManager.switchToLibrary(1) == 0) {
        IGraphicsLibrary* sfmlLib = libManager.getCurrentLibrary();
        if (sfmlLib) {
            std::cout << "Current library: " << libManager.getLibraryName(1) << std::endl;
            std::cout << "Initializing SFML..." << std::endl;
            int result = sfmlLib->initialize();
            std::cout << "SFML initialize result: " << result << std::endl;
            
            if (result == 0) {
                std::cout << "SFML initialized successfully!" << std::endl;
                std::this_thread::sleep_for(std::chrono::seconds(2));
                
                std::cout << "Shutting down SFML..." << std::endl;
                sfmlLib->shutdown();
                std::cout << "SFML shutdown complete." << std::endl;
            }
        }
    }
    
    // Test switching to NCurses (index 0)
    std::cout << "\n=== Testing switch to NCurses (index 0) ===" << std::endl;
    if (libManager.switchToLibrary(0) == 0) {
        IGraphicsLibrary* ncursesLib = libManager.getCurrentLibrary();
        if (ncursesLib) {
            std::cout << "Current library: " << libManager.getLibraryName(0) << std::endl;
            std::cout << "Initializing NCurses..." << std::endl;
            int result = ncursesLib->initialize();
            std::cout << "NCurses initialize result: " << result << std::endl;
            
            if (result == 0) {
                std::cout << "NCurses initialized successfully!" << std::endl;
                std::this_thread::sleep_for(std::chrono::seconds(1));
                
                std::cout << "Shutting down NCurses..." << std::endl;
                ncursesLib->shutdown();
                std::cout << "NCurses shutdown complete." << std::endl;
            }
        }
    }
    
    std::cout << "\n=== Test Complete ===" << std::endl;
    return 0;
}
